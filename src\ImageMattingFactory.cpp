﻿#include "ImageMattingFactory.h"
#include "ImageMatting.h"
#include "ImageMattingOnnx.h"
#include "ImageMattingTensorRt.h"
#include "Helpers.h"
#include <NvInfer.h>
#include <NvOnnxParser.h>
#include <iostream>
#include <filesystem>
#include <algorithm>
#include <regex>
#include <mutex>
#include <fstream>
#include <minwindef.h>
#include <libloaderapi.h>
#include <vector>
#include <chrono>
#include <thread>
#include <iomanip>

// Static member definitions
std::map<ModelType, ModelTypeConfig> ImageMattingFactory::s_modelConfigs;
std::vector<ModelInfo> ImageMattingFactory::s_availableModels;
std::map<std::tuple<ModelType, int, int, int, int, InferenceBackend, cudaStream_t>, std::vector<std::unique_ptr<MattingInstance>>> ImageMattingFactory::s_instances;
std::mutex ImageMattingFactory::s_instancesMutex;
bool ImageMattingFactory::s_modelsScanned = false;
std::mutex ImageMattingFactory::s_scanMutex;



// NEW UNIFIED INTERFACE IMPLEMENTATIONS

ImageMatting* ImageMattingFactory::GetInstance(
    ModelType modelType,
    int imageWidth,
    int imageHeight,
    cudaStream_t stream,
    InferenceBackend backend,
    BestModelSelectionMethod selectionMethod,
    bool useModelSize) {

    // Initialize model configs if not done yet
    InitializeModelConfigs();

    // Get default normalization for this model type
    auto configIt = s_modelConfigs.find(modelType);
    if (configIt == s_modelConfigs.end()) {
        std::cerr << "Error: Unknown model type" << std::endl;
        return nullptr;
    }

    return GetInstance(modelType, imageWidth, imageHeight, configIt->second.defaultNormalization, stream, backend, selectionMethod, useModelSize);
}

std::unique_ptr<ImageMatting> ImageMattingFactory::Init(
    ModelType modelType,
    int imageWidth,
    int imageHeight,
    cudaStream_t stream,
    InferenceBackend backend,
    BestModelSelectionMethod selectionMethod,
    bool useModelSize) {

    // Initialize model configs if not done yet
    InitializeModelConfigs();

    // Get default normalization for this model type
    auto configIt = s_modelConfigs.find(modelType);
    if (configIt == s_modelConfigs.end()) {
        std::cerr << "Error: Unknown model type" << std::endl;
        return nullptr;
    }

    return Init(modelType, imageWidth, imageHeight, configIt->second.defaultNormalization, stream, backend, selectionMethod, useModelSize);
}

ImageMatting* ImageMattingFactory::GetInstance(
    ModelType modelType,
    int imageWidth,
    int imageHeight,
    const NormalizationParams& normParams,
    cudaStream_t stream,
    InferenceBackend backend,
    BestModelSelectionMethod selectionMethod,
    bool useModelSize) {

    try {
        // Initialize model configs if not done yet
        InitializeModelConfigs();

        // Scan models if not done yet
        ScanAvailableModels();

        // Find the best model for these dimensions
        ModelInfo bestModel = FindBestModel(modelType, imageWidth, imageHeight, selectionMethod, backend);
        if (bestModel.path.empty()) {
            std::cerr << "Error: Could not find a suitable model for type " << static_cast<int>(modelType)
                      << " and dimensions " << imageWidth << "x" << imageHeight << std::endl;
            return nullptr;
        }

        // If useModelSize is true, override imageWidth/imageHeight with model size
        if (useModelSize) {
            imageWidth = bestModel.width;
            imageHeight = bestModel.height;
        }

        // Create key for instance management (include modelType, model size, image size, backend, stream)
        auto key = std::make_tuple(modelType, bestModel.width, bestModel.height, imageWidth, imageHeight, backend, stream);

        // Try to find an existing available instance
        {
            std::lock_guard<std::mutex> lock(s_instancesMutex);

            auto it = s_instances.find(key);
            if (it != s_instances.end()) {
                // Look for an available instance
                for (auto& instance : it->second) {
                    if (!instance->inUse) {
                        instance->inUse = true;
                        std::cout << "Reusing existing model instance: " << bestModel.width << "x" << bestModel.height
                                  << ", image: " << imageWidth << "x" << imageHeight
                                  << ", backend: " << static_cast<int>(backend) << std::endl;
                        return instance->imageMatting.get();
                    }
                }
            }
        }

        // No available instance found, create a new one
        std::cout << "Creating new model instance: " << bestModel.width << "x" << bestModel.height
                  << ", image: " << imageWidth << "x" << imageHeight
                  << ", backend: " << static_cast<int>(backend) << std::endl;

        auto instance = std::make_unique<MattingInstance>();
        instance->type = modelType;
        instance->width = bestModel.width;
        instance->height = bestModel.height;
        instance->inUse = true;
        // Store the stream for debugging/validation
        // (If MattingInstance does not have a stream member, add one)
        instance->stream = stream;

        // Create the ImageMatting instance
        instance->imageMatting = CreateInstance(bestModel, imageWidth, imageHeight, normParams, stream, backend);
        if (!instance->imageMatting) {
            std::cerr << "Failed to create ImageMatting instance" << std::endl;
            return nullptr;
        }

        ImageMatting* result = instance->imageMatting.get();

        // Add the instance to the map
        {
            std::lock_guard<std::mutex> lock(s_instancesMutex);

            auto it = s_instances.find(key);
            if (it == s_instances.end()) {
                s_instances[key] = std::vector<std::unique_ptr<MattingInstance>>();
                it = s_instances.find(key);
            }
            it->second.push_back(std::move(instance));
        }

        return result;
    }
    catch (const std::exception& e) {
        std::cerr << "Exception in ImageMattingFactory::Init: " << e.what() << std::endl;
        return nullptr;
    }
}

void ImageMattingFactory::ReturnInstance(ImageMatting* instance) {
    if (!instance) return;

    std::lock_guard<std::mutex> lock(s_instancesMutex);

    // Find the instance and mark it as not in use
    for (auto& pair : s_instances) {
        for (auto& inst : pair.second) {
            if (inst->imageMatting.get() == instance) {
                inst->inUse = false;
                return;
            }
        }
    }

    std::cerr << "Warning: Attempted to return unknown instance" << std::endl;
}

std::unique_ptr<ImageMatting> ImageMattingFactory::Init(
    ModelType modelType,
    int imageWidth,
    int imageHeight,
    const NormalizationParams& normParams,
    cudaStream_t stream,
    InferenceBackend backend,
    BestModelSelectionMethod selectionMethod,
    bool useModelSize) {

    try {
        // Initialize model configs if not done yet
        InitializeModelConfigs();

        // Scan models if not done yet
        ScanAvailableModels();

        // Find the best model for these dimensions
        ModelInfo bestModel = FindBestModel(modelType, imageWidth, imageHeight, selectionMethod, backend);
        if (bestModel.path.empty()) {
            std::cerr << "Error: Could not find a suitable model for type " << static_cast<int>(modelType)
                      << " and dimensions " << imageWidth << "x" << imageHeight << std::endl;
            return nullptr;
        }

        // If useModelSize is true, override imageWidth/imageHeight with model size
        if (useModelSize) {
            imageWidth = bestModel.width;
            imageHeight = bestModel.height;
        }

        std::cout << "Creating new unmanaged model instance: " << bestModel.width << "x" << bestModel.height << std::endl;

        // Create the ImageMatting instance directly (not managed by factory)
        return CreateInstance(bestModel, imageWidth, imageHeight, normParams, stream, backend);
    }
    catch (const std::exception& e) {
        std::cerr << "Exception in ImageMattingFactory::Init: " << e.what() << std::endl;
        return nullptr;
    }
}

std::unique_ptr<ImageMatting> ImageMattingFactory::InitOnnx(
    const wchar_t* modelPath,
    int modelWidth,
    int modelHeight,
    int imageWidth,
    int imageHeight,
    const NormalizationParams& normParams,
    bool isRgba,
    ResizeMethod resizeMethod,
    cudaStream_t externalStream) {

    try {
        auto implementation = std::make_unique<ImageMattingOnnx>();

        if (implementation->Init(modelPath, modelWidth, modelHeight, imageWidth, imageHeight, normParams, isRgba, resizeMethod, externalStream)) {
            std::cout << "ONNX Runtime implementation initialized successfully" << std::endl;
            return std::move(implementation);
        } else {
            std::cerr << "Failed to initialize ONNX Runtime implementation" << std::endl;
            return nullptr;
        }
    }
    catch (const std::exception& e) {
        std::cerr << "Exception creating ONNX implementation: " << e.what() << std::endl;
        return nullptr;
    }
}

std::unique_ptr<ImageMatting> ImageMattingFactory::InitTensorRt(
    const wchar_t* enginePath,
    int modelWidth,
    int modelHeight,
    int imageWidth,
    int imageHeight,
    const NormalizationParams& normParams,
    bool isRgba,
    ResizeMethod resizeMethod,
    cudaStream_t externalStream) {

    try {
        auto implementation = std::make_unique<ImageMattingTensorRt>();

        if (implementation->Init(enginePath, modelWidth, modelHeight, imageWidth, imageHeight, normParams, isRgba, resizeMethod, externalStream)) {
            std::cout << "TensorRT implementation initialized successfully" << std::endl;
            return std::move(implementation);
        } else {
            std::wcerr << L"Failed to initialize TensorRT implementation with engine: " << enginePath << std::endl;
            return nullptr;
        }
    }
    catch (const std::exception& e) {
        std::cerr << "Exception creating TensorRT implementation: " << e.what() << std::endl;
        return nullptr;
    }
}

std::wstring ImageMattingFactory::GetFileExtension(const wchar_t* filePath) {
    std::wstring path(filePath);
    size_t dotPos = path.find_last_of(L'.');
    if (dotPos != std::wstring::npos) {
        return path.substr(dotPos);
    }
    return L"";
}

bool ImageMattingFactory::FileExists(const wchar_t* filePath) {
    try {
        return std::filesystem::exists(filePath);
    }
    catch (...) {
        return false;
    }
}

bool ImageMattingFactory::IsTensorRtAvailable() {
    try {
        auto testImplementation = std::make_unique<ImageMattingTensorRt>();
        return true;
    }
    catch (...) {
        return false;
    }
}

bool ImageMattingFactory::IsOnnxRuntimeAvailable() {
    try {
        auto testImplementation = std::make_unique<ImageMattingOnnx>();
        return true;
    }
    catch (...) {
        return false;
    }
}

bool ImageMattingFactory::CreateTensorRtEngine(const wchar_t* onnxPath, const wchar_t* enginePath, int poolSizeMB, const std::vector<std::string>& additionalArgs) {
    try {
        std::cout << "Creating TensorRT engine from ONNX model using trtexec..." << std::endl;

        std::string onnxPathStr = ConvertWCharToChar(onnxPath);
        std::string enginePathStr = ConvertWCharToChar(enginePath);

        std::cout << "ONNX path: " << onnxPathStr << std::endl;
        std::cout << "Engine path: " << enginePathStr << std::endl;
        std::cout << "Pool size: " << poolSizeMB << " MB" << std::endl;

        // Check if ONNX file exists
        if (!std::filesystem::exists(onnxPathStr)) {
            std::cerr << "ONNX file not found: " << onnxPathStr << std::endl;
            return false;
        }

        // Find trtexec executable
        std::string trtexecPath = FindTrtexecExecutable();
        if (trtexecPath.empty()) {
            std::cerr << "trtexec not found! Please install TensorRT and ensure trtexec is in your PATH" << std::endl;
            std::cerr << "Download TensorRT from: https://developer.nvidia.com/tensorrt" << std::endl;
            return false;
        }

        std::cout << "Found trtexec: " << trtexecPath << std::endl;

        // Check if this is a dynamic ONNX model that needs shape specification
        bool isDynamicModel = false;
        std::filesystem::path modelPath(onnxPathStr);
        std::string filename = modelPath.filename().string();
        std::transform(filename.begin(), filename.end(), filename.begin(), ::tolower);

        if (filename.find("rmbg2_0") != std::string::npos) {
            isDynamicModel = true;
            std::cout << "Detected dynamic ONNX model: " << filename << std::endl;
        }

        // Build trtexec command. ATTENTION: Inspyrenet does not work with TensorRt 10.11, even in fp32!
        std::vector<std::string> cmdArgs;
        cmdArgs.push_back(trtexecPath);
        cmdArgs.push_back("--onnx=" + onnxPathStr);
        cmdArgs.push_back("--saveEngine=" + enginePathStr);
        //cmdArgs.push_back("--verbose");

        // Add default memory pool size if not overridden by additionalArgs
        bool hasMemPoolSize = false;
        for (const auto& arg : additionalArgs) {
            if (arg.find("--memPoolSize") != std::string::npos) {
                hasMemPoolSize = true;
                break;
            }
        }
        if (!hasMemPoolSize) {
            cmdArgs.push_back("--memPoolSize=workspace:" + std::to_string(poolSizeMB));
        }

        // Add model-specific additional arguments
        for (const auto& arg : additionalArgs) {
            cmdArgs.push_back(arg);
        }

        // Add shape specification for dynamic models
        if (isDynamicModel) {
            // For RMBG2_0: input: pixel_values float32[1,3,height,width] and output float32[1,1,height,width]
            // Set to 1024x1024 as the standard size for RMBG models
            cmdArgs.push_back("--shapes=pixel_values:1x3x1024x1024");
            std::cout << "Added shape specification for dynamic model: input:1x3x1024x1024" << std::endl;
        }

        // Convert command to string for display
        std::string cmdStr;
        for (const auto& arg : cmdArgs) {
            cmdStr += arg + " ";
        }
        std::cout << "Executing command: " << cmdStr << std::endl;

        // Execute trtexec
        bool success = ExecuteTrtexecCommand(cmdArgs);

        if (success) {
            // Verify engine file was created
            if (std::filesystem::exists(enginePathStr)) {
                auto fileSize = std::filesystem::file_size(enginePathStr);
                double sizeMB = static_cast<double>(fileSize) / (1024.0 * 1024.0);
                std::cout << L"✅ TensorRT engine created successfully: " << enginePathStr << std::endl;
                std::cout << "Engine size: " << std::fixed << std::setprecision(1) << sizeMB << " MB" << std::endl;
                return true;
            }
            else {
                std::cerr << L"❌ Engine file was not created" << std::endl;
                return false;
            }
        }
        else {
            std::cerr << L"❌ trtexec conversion failed" << std::endl;
            return false;
        }
    }
    catch (const std::exception& e) {
        std::cerr << "Exception in CreateTensorRtEngine: " << e.what() << std::endl;
        return false;
    }
}

std::string ImageMattingFactory::FindTrtexecExecutable() {
    // Common locations for trtexec
    std::vector<std::string> possiblePaths = {
        "trtexec",  // If in PATH
        "trtexec.exe",  // Windows
        "/usr/src/tensorrt/bin/trtexec",  // Docker
        "/opt/tensorrt/bin/trtexec",  // Linux install
        "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\TensorRT\\bin\\trtexec.exe",  // Windows install
    };

    for (const auto& path : possiblePaths) {
        if (TestTrtexecPath(path)) {
            return path;
        }
    }

    return "";  // Not found
}

bool ImageMattingFactory::TestTrtexecPath(const std::string& path) {
    try {
        // Test if trtexec exists and is executable by running with --help
        std::vector<std::string> testCmd = { path, "--help" };

        // Use a quick test with timeout
        return ExecuteTrtexecCommand(testCmd, true, 5000);  // 5 second timeout for test
    }
    catch (...) {
        return false;
    }
}

bool ImageMattingFactory::ExecuteTrtexecCommand(const std::vector<std::string>& cmdArgs, bool isTest, int timeoutMs) {
    // Build command line string
    std::string cmdLine;
    for (size_t i = 0; i < cmdArgs.size(); ++i) {
        if (i > 0) cmdLine += " ";
        // Quote arguments that contain spaces
        if (cmdArgs[i].find(' ') != std::string::npos) {
            cmdLine += "\"" + cmdArgs[i] + "\"";
        }
        else {
            cmdLine += cmdArgs[i];
        }
    }

    STARTUPINFOA si = {};
    PROCESS_INFORMATION pi = {};
    si.cb = sizeof(si);

    if (!isTest) {
        si.dwFlags = STARTF_USESTDHANDLES;
        si.hStdOutput = GetStdHandle(STD_OUTPUT_HANDLE);
        si.hStdError = GetStdHandle(STD_ERROR_HANDLE);
    }

    std::cout << "Final command line: [" << cmdLine << "]" << std::endl;

    BOOL result = CreateProcessA(
        nullptr,                    // No module name (use command line)
        &cmdLine[0],               // Command line
        nullptr,                   // Process handle not inheritable
        nullptr,                   // Thread handle not inheritable
        TRUE,                      // Set handle inheritance to TRUE
        0,                         // No creation flags
        nullptr,                   // Use parent's environment block
        nullptr,                   // Use parent's starting directory
        &si,                       // Pointer to STARTUPINFO structure
        &pi                        // Pointer to PROCESS_INFORMATION structure
    );

    if (!result) {
        if (!isTest) {
            std::cerr << "Failed to create process. Error: " << GetLastError() << std::endl;
        }
        return false;
    }

    // Wait for process completion with timeout
    DWORD waitResult = WaitForSingleObject(pi.hProcess, timeoutMs > 0 ? timeoutMs : INFINITE);

    bool success = false;
    if (waitResult == WAIT_OBJECT_0) {
        DWORD exitCode;
        if (GetExitCodeProcess(pi.hProcess, &exitCode)) {
            success = (exitCode == 0);
        }
    }
    else if (waitResult == WAIT_TIMEOUT) {
        if (!isTest) {
            std::cerr << "Process timed out" << std::endl;
        }
        TerminateProcess(pi.hProcess, 1);
    }

    // Close process and thread handles
    CloseHandle(pi.hProcess);
    CloseHandle(pi.hThread);

    return success;
}

int ImageMattingFactory::CreateAllTensorRtEngines(bool forceRecreate) {
    try {
        std::cout << "=== Creating TensorRT engines for all ONNX models ===" << std::endl;
        std::cout << "Force recreate: " << (forceRecreate ? "Yes" : "No") << std::endl;

        // Initialize model configs and scan models
        InitializeModelConfigs();
        ScanAvailableModels();

        if (!IsTensorRtAvailable()) {
            std::cerr << "Error: TensorRT is not available" << std::endl;
            return 0;
        }

        int enginesCreated = 0;
        int enginesSkipped = 0;
        int enginesFailed = 0;

        // Filter for ONNX models only
        std::vector<ModelInfo> onnxModels;
        for (const auto& model : s_availableModels) {
            std::wstring ext = GetFileExtension(model.path.c_str());
            std::transform(ext.begin(), ext.end(), ext.begin(), ::towlower);
            if (ext == L".onnx" && model.width == model.height) { // Skip non-square models since they don't seem to work well in fp16.
                onnxModels.push_back(model);
            }
        }

        std::cout << "Found " << onnxModels.size() << " ONNX models to process" << std::endl;

        for (const auto& model : onnxModels) {
            std::cout << "\n--- Processing model ---" << std::endl;
            std::cout << "Model: " << ConvertWCharToChar(model.path.c_str()) << std::endl;
            std::cout << "Type: " << static_cast<int>(model.type) << std::endl;
            std::cout << "Dimensions: " << model.width << "x" << model.height << std::endl;

            // Generate engine path
            std::wstring enginePath = model.path;
            size_t dotPos = enginePath.find_last_of(L'.');
            if (dotPos != std::wstring::npos) {
                enginePath = enginePath.substr(0, dotPos) + L".engine";
            }

            std::cout << "Target engine: " << ConvertWCharToChar(enginePath.c_str()) << std::endl;

            // Check if engine already exists
            bool engineExists = FileExists(enginePath.c_str());
            if (engineExists && !forceRecreate) {
                std::cout << "Engine already exists, skipping" << std::endl;
                enginesSkipped++;
                continue;
            }

            if (engineExists && forceRecreate) {
                std::cout << "Engine exists but force recreate is enabled" << std::endl;
            }

            // Get model-specific configuration
            std::vector<std::string> modelArgs;
            int modelPoolSize = 2048; // Default pool size
            auto configIt = s_modelConfigs.find(model.type);
            if (configIt != s_modelConfigs.end()) {
                modelArgs = configIt->second.trtexecArgs;
                modelPoolSize = configIt->second.poolSizeMB;

                std::cout << "Using model-specific pool size: " << modelPoolSize << " MB" << std::endl;
                if (!modelArgs.empty()) {
                    std::cout << "Using model-specific trtexec arguments: ";
                    for (const auto& arg : modelArgs) {
                        std::cout << arg << " ";
                    }
                    std::cout << std::endl;
                }
            }

            // Create the engine
            std::cout << "Creating TensorRT engine (this may take several minutes)..." << std::endl;
            auto startTime = std::chrono::high_resolution_clock::now();

            bool success = CreateTensorRtEngine(model.path.c_str(), enginePath.c_str(), modelPoolSize, modelArgs);

            auto endTime = std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::seconds>(endTime - startTime);

            if (success) {
                std::cout << "SUCCESS: Engine created in " << duration.count() << " seconds" << std::endl;

                // Get file size for reporting
                try {
                    auto fileSize = std::filesystem::file_size(enginePath);
                    std::cout << "Engine size: " << (fileSize / (1024 * 1024)) << " MB" << std::endl;
                }
                catch (...) {
                    std::cout << "Engine size: Unable to determine" << std::endl;
                }

                enginesCreated++;
            }
            else {
                std::cout << "FAILED: Engine creation failed after " << duration.count() << " seconds" << std::endl;
                enginesFailed++;
            }
        }

        std::cout << "\n=== Engine Creation Summary ===" << std::endl;
        std::cout << "Total ONNX models found: " << onnxModels.size() << std::endl;
        std::cout << "Engines created: " << enginesCreated << std::endl;
        std::cout << "Engines skipped (already exist): " << enginesSkipped << std::endl;
        std::cout << "Engine creation failures: " << enginesFailed << std::endl;

        if (enginesFailed > 0) {
            std::cout << "WARNING: Some engines failed to create. Check the error messages above." << std::endl;
        }

        return enginesCreated;
    }
    catch (const std::exception& e) {
        std::cerr << "Exception in CreateAllTensorRtEngines: " << e.what() << std::endl;
        return 0;
    }
}

std::vector<std::pair<ModelInfo, bool>> ImageMattingFactory::GetModelEngineStatus() {
    std::vector<std::pair<ModelInfo, bool>> result;

    try {
        // Initialize model configs and scan models
        InitializeModelConfigs();
        ScanAvailableModels();

        for (const auto& model : s_availableModels) {
            std::wstring ext = GetFileExtension(model.path.c_str());
            std::transform(ext.begin(), ext.end(), ext.begin(), ::towlower);

            bool hasEngine = false;

            if (ext == L".onnx") {
                // Check if corresponding .engine file exists
                std::wstring enginePath = model.path;
                size_t dotPos = enginePath.find_last_of(L'.');
                if (dotPos != std::wstring::npos) {
                    enginePath = enginePath.substr(0, dotPos) + L".engine";
                }
                hasEngine = FileExists(enginePath.c_str());
            }
            else if (ext == L".engine" || ext == L".trt") {
                // This is already a TensorRT engine
                hasEngine = true;
            }

            result.emplace_back(model, hasEngine);
        }
    }
    catch (const std::exception& e) {
        std::cerr << "Exception in GetModelEngineStatus: " << e.what() << std::endl;
    }

    return result;
}

// HELPER FUNCTION IMPLEMENTATIONS

void ImageMattingFactory::InitializeModelConfigs() {
    static bool initialized = false;
    static std::mutex initMutex;

    std::lock_guard<std::mutex> lock(initMutex);
    if (initialized) return;

    {
        // InsPyReNet configuration - fp16 disabled due to precision issues
        s_modelConfigs[ModelType::INSPYRENET] = ModelTypeConfig(
            "InsPyReNet_(\\d+)x(\\d+)\\.(onnx|trt|engine)",
            NormalizationParams::ImageNet(),
            false,  // isRgba
            true,   // hasMultipleSizes
            ResizeMethod::EXTEND_SHRINK_LANCZOS,  // Use extend/shrink with Lanczos
            {},     // No additional trtexec args (fp16 disabled for this model)
            2048    // poolSizeMB
        );

        // IndexNet configuration - use 1024 MB workspace for better compatibility
        s_modelConfigs[ModelType::INDEXNET] = ModelTypeConfig(
            "indexnet_matting_(\\d+)x(\\d+)\\.(onnx|trt|engine)",
            NormalizationParams::ImageNet(),
            true,   // isRgba
            true,   // hasMultipleSizes
            ResizeMethod::EXTEND_SHRINK_LANCZOS,  // Use extend/shrink with Lanczos
			{ "--fp16" },						 // Enable fp16 for IndexNet
			2048 								 // poolSizeMB 
        );

        // RMBG1_4 configuration - can use fp16 for better performance
        s_modelConfigs[ModelType::RMBG1_4] = ModelTypeConfig(
            "rmbg1_4.*\\.(onnx|trt|engine)",
            NormalizationParams::ZeroToOne(),
            false,  // isRgba
            false,  // hasMultipleSizes (fixed 1024x1024)
            ResizeMethod::STRETCH_ASPECT_RATIO_PAD,  // Use stretch with aspect ratio preservation and padding
            {"--fp16"},  // Enable fp16 for RMBG1_4
            2048    // poolSizeMB
        );

        // RMBG2_0 configuration - can use fp16 for better performance
        s_modelConfigs[ModelType::RMBG2_0] = ModelTypeConfig(
            "rmbg2_0.*\\.(onnx|trt|engine)",
            NormalizationParams::ImageNet(),
            false,  // isRgba
            false,  // hasMultipleSizes (fixed 1024x1024)
            ResizeMethod::STRETCH_ASPECT_RATIO_PAD,  // Use stretch with aspect ratio preservation and padding
            {"--fp16"},  // Enable fp16 for RMBG2_0
            2048    // poolSizeMB
        );
    }

    initialized = true;
}

void ImageMattingFactory::ScanAvailableModels() {
    std::lock_guard<std::mutex> lock(s_scanMutex);

    if (s_modelsScanned) {
        return; // Already scanned
    }

    std::cout << "Scanning available models..." << std::endl;

    // Get executable directory
    wchar_t exePath[MAX_PATH];
    GetModuleFileNameW(NULL, exePath, MAX_PATH);
    std::filesystem::path exeDir = std::filesystem::path(exePath).parent_path();

    s_availableModels.clear();

    std::filesystem::path modelsDir = exeDir / "ModelsMatting";

    if (!std::filesystem::exists(modelsDir)) {
        std::wcout << L"Warning: Models directory not found: " << modelsDir.wstring() << std::endl;
        return;
    }

    std::cout << "Scanning models ..." << std::endl;

    // Scan each model type
    for (const auto& configPair : s_modelConfigs) {
        ModelType modelType = configPair.first;
        const ModelTypeConfig& config = configPair.second;
        try {
            for (const auto& entry : std::filesystem::directory_iterator(modelsDir)) {
                if (entry.is_regular_file()) {
                    std::string filename = entry.path().filename().string();
                    std::smatch matches;

                    if (std::regex_search(filename, matches, config.filenamePattern)) {
                        if (config.hasMultipleSizes && matches.size() > 2) {
                            // Extract dimensions from filename
                            int width = std::stoi(matches[1].str());
                            int height = std::stoi(matches[2].str());

                            ModelInfo model(entry.path().wstring(), modelType, width, height);
                            s_availableModels.push_back(model);

                            std::cout << "Found model: " << filename
                                      << " (" << width << "x" << height << ")" << std::endl;
                        } else if (!config.hasMultipleSizes) {
                            // Fixed size models
                            int width, height;
                            if (modelType == ModelType::RMBG1_4 || modelType == ModelType::RMBG2_0) {
                                width = height = 1024; // Both RMBG models use 1024x1024
                            } else {
                                width = height = 512; // Default fallback
                            }

                            ModelInfo model(entry.path().wstring(), modelType, width, height);
                            s_availableModels.push_back(model);

                            std::cout << "Found fixed-size model: " << filename
                                      << " (" << width << "x" << height << ")" << std::endl;
                        }
                    }
                }
            }
        }
        catch (const std::filesystem::filesystem_error& ex) {
            std::cerr << "Filesystem error scanning models directory : " << ex.what() << std::endl;
        }
    }

    std::cout << "Found " << s_availableModels.size() << " total models" << std::endl;
    s_modelsScanned = true;
}

ModelInfo ImageMattingFactory::FindBestModel(ModelType modelType, int imageWidth, int imageHeight, BestModelSelectionMethod method, InferenceBackend backend) {
    // Filter models by type and backend compatibility
    std::vector<ModelInfo> typeModels;
    for (const auto& model : s_availableModels) {
        if (model.type != modelType) continue;
        std::wstring ext = GetFileExtension(model.path.c_str());
        std::transform(ext.begin(), ext.end(), ext.begin(), ::towlower);
        bool backendOk = false;
        switch (backend) {
            case InferenceBackend::ONNX_ONLY:
                backendOk = (ext == L".onnx");
                break;
            case InferenceBackend::TENSORRT_ONLY:
            case InferenceBackend::TENSORRT_WITH_FALLBACK:
                backendOk = (ext == L".engine" || ext == L".trt");
                break;
            case InferenceBackend::AUTO:
            default:
                backendOk = (ext == L".onnx" || ext == L".engine" || ext == L".trt");
                break;
        }
        if (backendOk) {
            typeModels.push_back(model);
        }
    }

    if (typeModels.empty()) {
        std::cerr << "No models found for type " << static_cast<int>(modelType) << std::endl;
        return ModelInfo(); // Return empty model
    }

    // Get model type configuration
    auto configIt = s_modelConfigs.find(modelType);
    if (configIt == s_modelConfigs.end()) {
        std::cerr << "Unknown model type configuration" << std::endl;
        return ModelInfo();
    }

    const ModelTypeConfig& config = configIt->second;

    // Selection logic based on method
    switch (method) {
        case BestModelSelectionMethod::FIXED_SIZE:
            // For fixed-size models, just return the first available
            return typeModels[0];
        case BestModelSelectionMethod::ASPECT_RATIO_CLOSEST_FIT: {
            // Select by aspect ratio similarity first, then by closest size (same as previous ASPECT_RATIO)
            float imageAspectRatio = static_cast<float>(imageWidth) / imageHeight;
            const float ASPECT_RATIO_TOLERANCE = 0.25f;
            std::vector<ModelInfo> matchingAspectRatioModels;
            for (const auto& model : typeModels) {
                if (std::abs(model.aspectRatio - imageAspectRatio) <= ASPECT_RATIO_TOLERANCE) {
                    matchingAspectRatioModels.push_back(model);
                }
            }
            if (!matchingAspectRatioModels.empty()) {
                std::sort(matchingAspectRatioModels.begin(), matchingAspectRatioModels.end(),
                    [imageWidth, imageHeight](const ModelInfo& a, const ModelInfo& b) {
                        float aScore = std::abs(static_cast<float>(a.width) / imageWidth - 1.0f) +
                                      std::abs(static_cast<float>(a.height) / imageHeight - 1.0f);
                        float bScore = std::abs(static_cast<float>(b.width) / imageWidth - 1.0f) +
                                      std::abs(static_cast<float>(b.height) / imageHeight - 1.0f);
                        return aScore < bScore;
                    });
                return matchingAspectRatioModels[0];
            }
            std::sort(typeModels.begin(), typeModels.end(),
                [imageAspectRatio](const ModelInfo& a, const ModelInfo& b) {
                    return std::abs(a.aspectRatio - imageAspectRatio) < std::abs(b.aspectRatio - imageAspectRatio);
                });
            return typeModels[0];
        }
        case BestModelSelectionMethod::ASPECT_RATIO_CLOSEST_BIGGER_SIZE: {
            // Select by aspect ratio similarity first, then by closest bigger size
            float imageAspectRatio = static_cast<float>(imageWidth) / imageHeight;
            const float ASPECT_RATIO_TOLERANCE = 0.25f;
            std::vector<ModelInfo> matchingAspectRatioModels;
            for (const auto& model : typeModels) {
                if (std::abs(model.aspectRatio - imageAspectRatio) <= ASPECT_RATIO_TOLERANCE) {
                    matchingAspectRatioModels.push_back(model);
                }
            }
            if (!matchingAspectRatioModels.empty()) {
                // Filter models that are >= image dimensions
                std::vector<ModelInfo> biggerModels;
                for (const auto& model : matchingAspectRatioModels) {
                    if (model.width >= imageWidth && model.height >= imageHeight) {
                        biggerModels.push_back(model);
                    }
                }
                if (!biggerModels.empty()) {
                    // Sort by closest size among bigger models
                    std::sort(biggerModels.begin(), biggerModels.end(),
                        [imageWidth, imageHeight](const ModelInfo& a, const ModelInfo& b) {
                            float aScore = std::abs(static_cast<float>(a.width) / imageWidth - 1.0f) +
                                          std::abs(static_cast<float>(a.height) / imageHeight - 1.0f);
                            float bScore = std::abs(static_cast<float>(b.width) / imageWidth - 1.0f) +
                                          std::abs(static_cast<float>(b.height) / imageHeight - 1.0f);
                            return aScore < bScore;
                        });
                    return biggerModels[0];
                }
                // If no bigger models with matching aspect ratio, fall back to closest fit
                std::sort(matchingAspectRatioModels.begin(), matchingAspectRatioModels.end(),
                    [imageWidth, imageHeight](const ModelInfo& a, const ModelInfo& b) {
                        float aScore = std::abs(static_cast<float>(a.width) / imageWidth - 1.0f) +
                                      std::abs(static_cast<float>(a.height) / imageHeight - 1.0f);
                        float bScore = std::abs(static_cast<float>(b.width) / imageWidth - 1.0f) +
                                      std::abs(static_cast<float>(b.height) / imageHeight - 1.0f);
                        return aScore < bScore;
                    });
                return matchingAspectRatioModels[0];
            }
            // No matching aspect ratio models, sort by aspect ratio similarity and prefer bigger models
            std::sort(typeModels.begin(), typeModels.end(),
                [imageAspectRatio, imageWidth, imageHeight](const ModelInfo& a, const ModelInfo& b) {
                    float aAspectDiff = std::abs(a.aspectRatio - imageAspectRatio);
                    float bAspectDiff = std::abs(b.aspectRatio - imageAspectRatio);
                    if (std::abs(aAspectDiff - bAspectDiff) < 0.01f) {
                        // Similar aspect ratios, prefer bigger models
                        bool aBigger = (a.width >= imageWidth && a.height >= imageHeight);
                        bool bBigger = (b.width >= imageWidth && b.height >= imageHeight);
                        if (aBigger && !bBigger) return true;
                        if (!aBigger && bBigger) return false;
                        // Both bigger or both smaller, choose closest
                        float aScore = std::abs(static_cast<float>(a.width) / imageWidth - 1.0f) +
                                      std::abs(static_cast<float>(a.height) / imageHeight - 1.0f);
                        float bScore = std::abs(static_cast<float>(b.width) / imageWidth - 1.0f) +
                                      std::abs(static_cast<float>(b.height) / imageHeight - 1.0f);
                        return aScore < bScore;
                    }
                    return aAspectDiff < bAspectDiff;
                });
            return typeModels[0];
        }
        case BestModelSelectionMethod::SMALLEST_FIT: {
            // IndexNet: Select smallest model that is >= target dimensions
            std::vector<ModelInfo> suitableModels;
            for (const auto& model : typeModels) {
                if (model.width >= imageWidth && model.height >= imageHeight) {
                    suitableModels.push_back(model);
                }
            }
            if (suitableModels.empty()) {
                std::cerr << "No model large enough for " << imageWidth << "x" << imageHeight << std::endl;
                return ModelInfo();
            }
            std::sort(suitableModels.begin(), suitableModels.end(),
                [](const ModelInfo& a, const ModelInfo& b) {
                    long long aArea = static_cast<long long>(a.width) * a.height;
                    long long bArea = static_cast<long long>(b.width) * b.height;
                    return aArea < bArea;
                });
            return suitableModels[0];
        }
        case BestModelSelectionMethod::SMALLEST_AREA_FIT: {
            // New: Find model with width >= imageWidth and height >= imageHeight, smallest area
            std::vector<ModelInfo> suitableModels;
            for (const auto& model : typeModels) {
                if (model.width >= imageWidth && model.height >= imageHeight) {
                    suitableModels.push_back(model);
                }
            }
            if (suitableModels.empty()) {
                std::cerr << "No model large enough for " << imageWidth << "x" << imageHeight << std::endl;
                return ModelInfo();
            }
            std::sort(suitableModels.begin(), suitableModels.end(),
                [](const ModelInfo& a, const ModelInfo& b) {
                    long long aArea = static_cast<long long>(a.width) * a.height;
                    long long bArea = static_cast<long long>(b.width) * b.height;
                    return aArea < bArea;
                });
            return suitableModels[0];
        }
        case BestModelSelectionMethod::DEFAULT:
        default:
            // Use per-model-type logic for backward compatibility
            if (!config.hasMultipleSizes) {
                return typeModels[0];
            }
            if (modelType == ModelType::INSPYRENET) {
                // Use aspect ratio closest fit logic (same as previous ASPECT_RATIO behavior)
                float imageAspectRatio = static_cast<float>(imageWidth) / imageHeight;
                const float ASPECT_RATIO_TOLERANCE = 0.25f;
                std::vector<ModelInfo> matchingAspectRatioModels;
                for (const auto& model : typeModels) {
                    if (std::abs(model.aspectRatio - imageAspectRatio) <= ASPECT_RATIO_TOLERANCE) {
                        matchingAspectRatioModels.push_back(model);
                    }
                }
                if (!matchingAspectRatioModels.empty()) {
                    std::sort(matchingAspectRatioModels.begin(), matchingAspectRatioModels.end(),
                        [imageWidth, imageHeight](const ModelInfo& a, const ModelInfo& b) {
                            float aScore = std::abs(static_cast<float>(a.width) / imageWidth - 1.0f) +
                                          std::abs(static_cast<float>(a.height) / imageHeight - 1.0f);
                            float bScore = std::abs(static_cast<float>(b.width) / imageWidth - 1.0f) +
                                          std::abs(static_cast<float>(b.height) / imageHeight - 1.0f);
                            return aScore < bScore;
                        });
                    return matchingAspectRatioModels[0];
                }
                std::sort(typeModels.begin(), typeModels.end(),
                    [imageAspectRatio](const ModelInfo& a, const ModelInfo& b) {
                        return std::abs(a.aspectRatio - imageAspectRatio) < std::abs(b.aspectRatio - imageAspectRatio);
                    });
                return typeModels[0];
            } else if (modelType == ModelType::INDEXNET) {
                // Use smallest fit logic
                std::vector<ModelInfo> suitableModels;
                for (const auto& model : typeModels) {
                    if (model.width >= imageWidth && model.height >= imageHeight) {
                        suitableModels.push_back(model);
                    }
                }
                if (suitableModels.empty()) {
                    std::cerr << "No IndexNet model large enough for " << imageWidth << "x" << imageHeight << std::endl;
                    return ModelInfo();
                }
                std::sort(suitableModels.begin(), suitableModels.end(),
                    [](const ModelInfo& a, const ModelInfo& b) {
                        long long aArea = static_cast<long long>(a.width) * a.height;
                        long long bArea = static_cast<long long>(b.width) * b.height;
                        return aArea < bArea;
                    });
                return suitableModels[0];
            }
            // Default: return first model
            return typeModels[0];
    }
}

std::unique_ptr<ImageMatting> ImageMattingFactory::CreateInstance(
    const ModelInfo& modelInfo,
    int imageWidth,
    int imageHeight,
    const NormalizationParams& normParams,
    cudaStream_t stream,
    InferenceBackend backend) {

    // Get model type configuration
    auto configIt = s_modelConfigs.find(modelInfo.type);
    if (configIt == s_modelConfigs.end()) {
        std::cerr << "Unknown model type configuration for CreateInstance" << std::endl;
        return nullptr;
    }

    const ModelTypeConfig& config = configIt->second;

    std::cout << "Creating instance for model: " << ConvertWCharToChar(modelInfo.path.c_str()) << std::endl;
    std::cout << "Model dimensions: " << modelInfo.width << "x" << modelInfo.height << std::endl;
    std::cout << "Image dimensions: " << imageWidth << "x" << imageHeight << std::endl;

    // Determine which backend to use based on preference and availability
    std::wstring modelPath = modelInfo.path;
    std::wstring extension = GetFileExtension(modelPath.c_str());
    std::transform(extension.begin(), extension.end(), extension.begin(), ::tolower);

    // Handle backend preference
    switch (backend) {
        case InferenceBackend::ONNX_ONLY:
            std::cout << "Backend preference: ONNX only" << std::endl;
            if (extension != L".onnx") {
                std::cerr << "Error: ONNX_ONLY backend requested but model is not .onnx: " << ConvertWCharToChar(modelPath.c_str()) << std::endl;
                return nullptr;
            }
            break;

        case InferenceBackend::TENSORRT_ONLY:
            std::cout << "Backend preference: TensorRT only" << std::endl;
            if (!IsTensorRtAvailable()) {
                std::cerr << "Error: TensorRT not available but TENSORRT_ONLY backend requested" << std::endl;
                return nullptr;
            }
            if (extension == L".onnx") {
                // Need to create TensorRT engine
                std::wstring enginePath = modelPath;
                size_t dotPos = enginePath.find_last_of(L'.');
                if (dotPos != std::wstring::npos) {
                    enginePath = enginePath.substr(0, dotPos) + L".engine";
                }

                if (!FileExists(enginePath.c_str())) {
                    std::cout << "Creating TensorRT engine from ONNX with 2048 MB pool size..." << std::endl;
                    if (!CreateTensorRtEngine(modelPath.c_str(), enginePath.c_str(), 2048, config.trtexecArgs)) {
                        std::cerr << "Failed to create TensorRT engine for TENSORRT_ONLY backend" << std::endl;
                        return nullptr;
                    }
                }
                modelPath = enginePath;
            }
            break;

        case InferenceBackend::TENSORRT_WITH_FALLBACK:
            std::cout << "Backend preference: TensorRT with ONNX fallback" << std::endl;
            if (IsTensorRtAvailable() && extension == L".onnx") {
                std::wstring enginePath = modelPath;
                size_t dotPos = enginePath.find_last_of(L'.');
                if (dotPos != std::wstring::npos) {
                    enginePath = enginePath.substr(0, dotPos) + L".engine";
                }

                if (FileExists(enginePath.c_str())) {
                    std::cout << "Using existing TensorRT engine: " << ConvertWCharToChar(enginePath.c_str()) << std::endl;
                    modelPath = enginePath;
                } else {
                    std::cout << "TensorRT engine not found. Creating from ONNX with 2048 MB pool size..." << std::endl;
                    if (CreateTensorRtEngine(modelPath.c_str(), enginePath.c_str(), 2048, config.trtexecArgs)) {
                        std::cout << "TensorRT engine created successfully: " << ConvertWCharToChar(enginePath.c_str()) << std::endl;
                        modelPath = enginePath;
                    } else {
                        std::cout << "Failed to create TensorRT engine, falling back to ONNX" << std::endl;
                    }
                }
            }
            break;

        case InferenceBackend::AUTO:
        default:
            std::cout << "Backend preference: Auto (prefer existing TensorRT, no creation)" << std::endl;
            if (IsTensorRtAvailable()) {
                if (extension == L".onnx") {
                    std::wstring enginePath = modelPath;
                    size_t dotPos = enginePath.find_last_of(L'.');
                    if (dotPos != std::wstring::npos) {
                        enginePath = enginePath.substr(0, dotPos) + L".engine";
                    }

                    if (FileExists(enginePath.c_str())) {
                        std::cout << "Using existing TensorRT engine: " << ConvertWCharToChar(enginePath.c_str()) << std::endl;
                        modelPath = enginePath;
                    } else {
                        std::cout << "TensorRT engine not found, using ONNX (no automatic creation in AUTO mode)" << std::endl;
                    }
                }
            }
            break;
    }

    // Create the actual instance based on file extension
    std::wstring finalExtension = GetFileExtension(modelPath.c_str());
    std::transform(finalExtension.begin(), finalExtension.end(), finalExtension.begin(), ::tolower);

    std::cout << "Initializing ImageMatting with file: " << ConvertWCharToChar(modelPath.c_str()) << std::endl;
    std::cout << "Detected extension: " << ConvertWCharToChar(finalExtension.c_str()) << std::endl;

    if (finalExtension == L".onnx") {
        std::cout << "Using ONNX Runtime implementation" << std::endl;
        return InitOnnx(modelPath.c_str(), modelInfo.width, modelInfo.height, imageWidth, imageHeight, normParams, config.isRgba, config.resizeMethod, stream);
    }
    else if (finalExtension == L".trt" || finalExtension == L".engine") {
        std::cout << "Using TensorRT implementation" << std::endl;
        return InitTensorRt(modelPath.c_str(), modelInfo.width, modelInfo.height, imageWidth, imageHeight, normParams, config.isRgba, config.resizeMethod, stream);
    }
    else {
        std::wcerr << L"Error: Unsupported model format: " << finalExtension << std::endl;
        std::wcerr << L"Supported formats: .onnx, .trt, .engine" << std::endl;
        return nullptr;
    }
}
